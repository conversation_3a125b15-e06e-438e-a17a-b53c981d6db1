<manifest xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- The INTERNET permission is required for development. Specifically,
         the Flutter tool needs it to communicate with the running application
         to allow setting breakpoints, to provide hot reload, etc.
    -->
    <application
        android:label="sunsynk_flutter"
        android:name="${applicationName}"
        android:icon="@mipmap/ic_launcher"
        package="net.quickconnectwireless.inverter">
    </application>
    <uses-permission android:name="android.permission.INTERNET"/>
</manifest>
