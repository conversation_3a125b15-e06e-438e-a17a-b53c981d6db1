import 'package:flutter/material.dart';
import 'package:killwatt/services/api_client.dart';

class LayoutMain extends StatelessWidget {
  final Widget child;

  const LayoutMain({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('killwatt'),
        actions: [
          PopupMenuButton<String>(
            onSelected: (value) async {
              if (value == 'logout') {
                final ApiClient apiClient = ApiClient();
                await apiClient.logout();
                // Add logout logic here - you may need to inject ApiClient
                Navigator.pushReplacementNamed(context, '/login');
              }
            },
            itemBuilder: (BuildContext context) => [
              const PopupMenuItem<String>(
                value: 'logout',
                child: Row(
                  children: [
                    Icon(Icons.logout),
                    SizedBox(width: 8),
                    Text('Logout'),
                  ],
                ),
              ),
            ],
            icon: const Icon(Icons.more_vert),
          ),
        ],
      ),
      body: child,
    );
  }
}
