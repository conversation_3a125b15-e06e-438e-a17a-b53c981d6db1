import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:localstore/localstore.dart';

import 'package:sunsynk_flutter/pages/login.dart';
import 'package:sunsynk_flutter/pages/signup.dart';
import 'package:sunsynk_flutter/pages/forgot_password.dart';
import 'package:sunsynk_flutter/pages/home.dart';
import 'package:sunsynk_flutter/layouts/main.dart';
import 'package:flutter/widgets.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  await SentryFlutter.init((options) {
    options.dsn =
        'https://<EMAIL>/3';
    // Adds request headers and IP for users, for more info visit:
    // https://docs.sentry.io/platforms/dart/guides/flutter/data-management/data-collected/
    options.sendDefaultPii = true;
    // Set tracesSampleRate to 1.0 to capture 100% of transactions for tracing.
    // We recommend adjusting this value in production.
    options.tracesSampleRate = 1.0;
    // Enable logs to be sent to Sentry
    options.enableLogs = true;
  }, appRunner: () => runApp(SentryWidget(child: App())));
  // you can also configure SENTRY_DSN, SENTRY_RELEASE, SENTRY_DIST, and
  // SENTRY_ENVIRONMENT via Dart environment variable (--dart-define)
}

class App extends StatelessWidget {
  const App({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Sunsynk',
      theme: ThemeData(
        brightness: Brightness.light,
        primaryColor: Colors.blue, // Your brand color
        // Other light theme settings
      ),
      darkTheme: ThemeData(
        brightness: Brightness.dark,
        primaryColor: Colors.blue, // Your brand color
        // Other dark theme settings
      ),
      themeMode: ThemeMode.system, // Respect system theme
      home: const LayoutMain(child: PageLogin()),
      // initialRoute: '/login',
      routes: {
        '/dashboard': (context) => const LayoutMain(child: PageHome()),
        '/login': (context) => const LayoutMain(child: PageLogin()),
        '/signup': (context) => const LayoutMain(child: PageSignup()),
        '/forgot-password': (context) => const LayoutMain(child: PageForgotPassword()),
      },
    );
  }
}
