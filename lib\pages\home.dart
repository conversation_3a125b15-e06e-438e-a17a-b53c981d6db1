import 'package:flutter/material.dart';
import 'dart:convert';
import 'dart:async'; // Add this import for Timer
import 'package:shared_preferences/shared_preferences.dart';
import 'package:killwatt/screens/soc_settings.dart';
import '../services/api_client.dart';

class PageHome extends StatefulWidget {
  const PageHome({super.key});

  @override
  State<PageHome> createState() => _PageHomeState();
}

class _PageHomeState extends State<PageHome> {
  final ApiClient _apiClient = ApiClient();
  List<dynamic>? inverters;
  dynamic selectedInverter;
  Map<String, dynamic>? weatherData;
  Map<String, dynamic>? lastInverterData;
  bool isLoading = true;
  Timer? _pollingTimer; // Timer for polling
  bool _isPolling = false; // Flag to prevent multiple concurrent requests
  bool _isSwitchingInverter = false;

  static const _pollingInterval = Duration(
    seconds: 30,
  ); // Poll every 30 seconds

  @override
  void initState() {
    super.initState();
    _loadInverters();
  }

  @override
  void dispose() {
    // Cancel the timer when the widget is disposed
    _pollingTimer?.cancel();
    super.dispose();
  }

  Future<void> _loadInverters() async {
    if (!mounted) return;

    setState(() {
      isLoading = true;
    });

    try {
      final fetchedInverters = await _apiClient.fetchInverters();

      if (mounted) {
        setState(() {
          inverters = fetchedInverters;
          if (inverters != null && inverters!.isNotEmpty) {
            selectedInverter = inverters![0];
            _loadInverterData(int.parse(selectedInverter['id'].toString()));
            _startPolling(int.parse(selectedInverter['id'].toString()));
          }
          isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error loading inverters: $e')));
      }
    }
  }

  // Start polling for inverter data
  void _startPolling(int inverterId) {
    // Cancel any existing timer
    _pollingTimer?.cancel();

    // Create a new timer that fires periodically
    _pollingTimer = Timer.periodic(_pollingInterval, (timer) {
      if (!_isPolling) {
        _refreshInverterData(inverterId);
      }
    });
  }

  // Refresh inverter data without changing the loading state
  Future<void> _refreshInverterData(int inverterId) async {
    if (!mounted || _isPolling) return;

    _isPolling = true;

    try {
      // Fetch last inverter data
      final data = await _apiClient.fetchLastInverterData(inverterId);

      // Fetch weather data
      final weatherInfo = await _apiClient.fetchWeather(inverterId);

      if (mounted) {
        setState(() {
          lastInverterData = data;
          weatherData = weatherInfo;
        });
      }
    } catch (e) {
      // Silently handle errors during polling to avoid too many notifications
    } finally {
      _isPolling = false;
    }
  }

  // Initial load of inverter data
  Future<void> _loadInverterData(int inverterId) async {
    if (!mounted) return;

    try {
      // Fetch last inverter data
      final data = await _apiClient.fetchLastInverterData(inverterId);

      // Fetch weather data
      final weatherInfo = await _apiClient.fetchWeather(inverterId);
      debugPrint('Weather data: $weatherInfo');

      if (mounted) {
        setState(() {
          lastInverterData = data;
          weatherData = weatherInfo;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading inverter data: $e')),
        );
      }
    }
  }

  // Callback for when a new inverter is selected
  void onInverterSelected(dynamic inverter) {
    setState(() {
      selectedInverter = inverter;
      _isSwitchingInverter = true;
    });

    final inverterId = int.parse(inverter['id'].toString());

    // Load inverter data for the selected inverter
    _loadInverterData(inverterId).then((_) {
      if (mounted) {
        setState(() {
          _isSwitchingInverter = false;
        });
      }
    });

    // Restart polling with the new inverter ID
    _startPolling(inverterId);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          if (isLoading)
            const Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('Loading inverters...', style: TextStyle(fontSize: 16)),
                ],
              ),
            )
          else
            SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  // Inverter selector dropdown
                  if (isLoading) ...[
                    const Center(child: CircularProgressIndicator()),
                    const SizedBox(height: 24),
                  ] else if (inverters != null && inverters!.isNotEmpty) ...[
                    const Text(
                      'Select Inverter:',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Expanded(
                          child: Container(
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            padding: const EdgeInsets.symmetric(horizontal: 12),
                            child: DropdownButton<dynamic>(
                              value: selectedInverter,
                              isExpanded: true,
                              underline:
                                  Container(), // Remove the default underline
                              onChanged: (newValue) {
                                if (newValue != null) {
                                  onInverterSelected(newValue);
                                }
                              },
                              items: inverters!.map<DropdownMenuItem<dynamic>>((
                                inverter,
                              ) {
                                return DropdownMenuItem<dynamic>(
                                  value: inverter,
                                  child: Text(
                                    inverter['nickname'] ??
                                        inverter['serial_number'] ??
                                        'Unknown',
                                  ),
                                );
                              }).toList(),
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        IconButton(
                          onPressed:
                              selectedInverter != null &&
                                  lastInverterData != null
                              ? () {
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) => SocSettingsScreen(
                                        inverterId: int.parse(
                                          selectedInverter!['id'].toString(),
                                        ),
                                        inverterName:
                                            selectedInverter!['nickname'],
                                        soc: lastInverterData!['SOC'],
                                      ),
                                    ),
                                  );
                                }
                              : null,
                          icon: const Icon(Icons.settings),
                          tooltip: 'SOC Settings',
                          style: IconButton.styleFrom(
                            backgroundColor:
                                selectedInverter != null &&
                                    lastInverterData != null
                                ? Theme.of(
                                    context,
                                  ).primaryColor.withOpacity(0.1)
                                : null,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 24),
                  ] else ...[
                    const Center(child: Text('No inverters available')),
                    const SizedBox(height: 24),
                  ],

                  // Display last inverter data if available
                  if (lastInverterData != null) ...[
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'Current Inverter Data:',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        // Add a refresh button
                        IconButton(
                          icon: const Icon(Icons.refresh),
                          onPressed: () {
                            if (selectedInverter != null) {
                              final inverterId = int.parse(
                                selectedInverter['id'].toString(),
                              );
                              _refreshInverterData(inverterId);
                            }
                          },
                          tooltip: 'Refresh data',
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('SOC: ${lastInverterData!['SOC'] ?? 'N/A'}%'),
                            Text(
                              'Load: ${lastInverterData!['Load_W'] ?? 'N/A'} W',
                            ),
                            Text('PV: ${lastInverterData!['PV_W'] ?? 'N/A'} W'),
                            Text(
                              'Grid: ${lastInverterData!['Grid_W'] ?? 'N/A'} W',
                            ),
                            Text(
                              'Battery: ${lastInverterData!['Battery_W'] ?? 'N/A'} W',
                            ),
                            Text(
                              'Serial Number: ${selectedInverter!['serial_number']}',
                            ),
                            Text(
                              'Timestamp: ${lastInverterData!['timestamp_utc'] != null ? _formatTimestamp(lastInverterData!['timestamp_utc']) : 'N/A'}',
                            ),
                            // Add a small indicator showing when data was last refreshed
                            Padding(
                              padding: const EdgeInsets.only(top: 8.0),
                              child: Text(
                                'Last updated: ${DateTime.now().toLocal().toString().substring(0, 19)}',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey[600],
                                  fontStyle: FontStyle.italic,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 24),
                  ],
                  if (lastInverterData == null) ...[
                    // Add a loading spinner
                    const Center(child: CircularProgressIndicator()),
                    const SizedBox(height: 16),
                    const Text(
                      'Loading inverter data...',
                      style: TextStyle(fontSize: 16),
                    ),
                  ],

                  // Display weather data if available
                  if (weatherData != null) ...[
                    const Text(
                      'Weather Data:',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            if (weatherData!['data'] != null &&
                                weatherData!['data'].isNotEmpty) ...[
                              Text(
                                'Temperature: ${weatherData!['data'][0]['provider_data']['OpenMeteo']['temperature'] ?? 'N/A'}°C',
                              ),
                              Text(
                                'Forecast GHI: ${weatherData!['data'][0]['provider_data']['OpenMeteo']['ghi'] ?? 'N/A'} W/m²',
                              ),
                              Text(
                                'Forecast watts: ${weatherData!['data'][0]['provider_data']['OpenMeteo']['pv_forecast_watts'] ?? 'N/A'} W',
                              ),
                            ] else ...[
                              const Text('No weather data available'),
                            ],
                          ],
                        ),
                      ),
                    ),
                  ],
                  if (weatherData == null) ...[
                    Center(child: CircularProgressIndicator()),
                  ],

                  // Boost section
                  if (selectedInverter != null && lastInverterData != null) ...[
                    const SizedBox(height: 24),
                    const Text(
                      'Battery Boost:',
                      style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.battery_std,
                                  size: 24,
                                  color: _getSocColor(lastInverterData!['SOC']?.toDouble() ?? 0.0),
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'Current SOC: ${lastInverterData!['SOC'] ?? 'N/A'}%',
                                  style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),
                            SizedBox(
                              width: double.infinity,
                              child: ElevatedButton.icon(
                                icon: const Icon(Icons.battery_charging_full),
                                label: const Text('Start Boost'),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.orange,
                                  foregroundColor: Colors.white,
                                ),
                                onPressed: () => _showBoostDialog(),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
          // Loading overlay when switching inverters
          if (_isSwitchingInverter)
            Container(
              color: Colors.black54,
              child: const Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text(
                      'Loading inverter data...',
                      style: TextStyle(color: Colors.white),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  // Helper method to format timestamp
  String _formatTimestamp(String timestamp) {
    final dateTime = DateTime.parse(timestamp).toLocal();
    return '${dateTime.toString().substring(0, 19)}';
  }

  // Helper method to get SOC color
  Color _getSocColor(double soc) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    if (soc >= 80) {
      return isDarkMode ? Colors.green.shade400 : Colors.green.shade600;
    } else if (soc >= 50) {
      return isDarkMode ? Colors.orange.shade400 : Colors.orange.shade600;
    } else {
      return isDarkMode ? Colors.red.shade400 : Colors.red.shade600;
    }
  }

  // Show boost dialog
  Future<void> _showBoostDialog() async {
    if (selectedInverter == null) return;

    final targetSoc = await showDialog<int>(
      context: context,
      builder: (context) => _buildBoostDialog(),
    );

    if (targetSoc == null) return;

    await _startBoost(targetSoc);
  }

  // Build boost dialog
  Widget _buildBoostDialog() {
    final controller = TextEditingController(text: '100');

    return AlertDialog(
      title: const Text('Boost Battery'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Text('Set target SOC percentage:'),
          const SizedBox(height: 16),
          TextField(
            controller: controller,
            keyboardType: TextInputType.number,
            decoration: const InputDecoration(
              suffix: Text('%'),
              hintText: 'Enter target SOC (10-100)',
              border: OutlineInputBorder(),
            ),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            final value = int.tryParse(controller.text);
            if (value != null && value >= 10 && value <= 100) {
              Navigator.of(context).pop(value);
            } else {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Please enter a valid SOC between 10 and 100'),
                ),
              );
            }
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.orange,
            foregroundColor: Colors.white,
          ),
          child: const Text('Start Boost'),
        ),
      ],
    );
  }

  // Start boost
  Future<void> _startBoost(int targetSoc) async {
    if (selectedInverter == null) return;

    setState(() {
      _isSwitchingInverter = true;
    });

    try {
      final inverterId = int.parse(selectedInverter!['id'].toString());

      await _apiClient.boost({
        'inverterId': inverterId,
        'soc': targetSoc,
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Boost to ${targetSoc}% started successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to start boost: ${e.toString().replaceAll('Exception: ', '')}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSwitchingInverter = false;
        });
      }
    }
  }
}
