import 'package:flutter/material.dart';
import 'dart:convert';
import 'dart:async'; // Add this import for Timer
import 'package:shared_preferences/shared_preferences.dart';
import 'package:sunsynk_flutter/screens/soc_settings.dart';
import '../services/api_client.dart';

class PageHome extends StatefulWidget {
  const PageHome({super.key});

  @override
  State<PageHome> createState() => _PageHomeState();
}

class _PageHomeState extends State<PageHome> {
  final ApiClient _apiClient = ApiClient();
  List<dynamic>? inverters;
  dynamic selectedInverter;
  Map<String, dynamic>? weatherData;
  Map<String, dynamic>? lastInverterData;
  bool isLoading = true;
  Timer? _pollingTimer; // Timer for polling
  bool _isPolling = false; // Flag to prevent multiple concurrent requests
  bool _isSwitchingInverter = false;

  static const _pollingInterval = Duration(
    seconds: 30,
  ); // Poll every 30 seconds

  @override
  void initState() {
    super.initState();
    _loadInverters();
  }

  @override
  void dispose() {
    // Cancel the timer when the widget is disposed
    _pollingTimer?.cancel();
    super.dispose();
  }

  Future<void> _loadInverters() async {
    if (!mounted) return;

    setState(() {
      isLoading = true;
    });

    try {
      final fetchedInverters = await _apiClient.fetchInverters();

      if (mounted) {
        setState(() {
          inverters = fetchedInverters;
          if (inverters != null && inverters!.isNotEmpty) {
            selectedInverter = inverters![0];
            _loadInverterData(int.parse(selectedInverter['id'].toString()));
            _startPolling(int.parse(selectedInverter['id'].toString()));
          }
          isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error loading inverters: $e')));
      }
    }
  }

  // Start polling for inverter data
  void _startPolling(int inverterId) {
    // Cancel any existing timer
    _pollingTimer?.cancel();

    // Create a new timer that fires periodically
    _pollingTimer = Timer.periodic(_pollingInterval, (timer) {
      if (!_isPolling) {
        _refreshInverterData(inverterId);
      }
    });
  }

  // Refresh inverter data without changing the loading state
  Future<void> _refreshInverterData(int inverterId) async {
    if (!mounted || _isPolling) return;

    _isPolling = true;

    try {
      // Fetch last inverter data
      final data = await _apiClient.fetchLastInverterData(inverterId);

      if (mounted) {
        setState(() {
          lastInverterData = data;
        });
      }
    } catch (e) {
      // Silently handle errors during polling to avoid too many notifications
      debugPrint('Error refreshing inverter data: $e');
    } finally {
      _isPolling = false;
    }
  }

  // Initial load of inverter data
  Future<void> _loadInverterData(int inverterId) async {
    if (!mounted) return;

    try {
      // Fetch last inverter data
      final data = await _apiClient.fetchLastInverterData(inverterId);

      // Fetch weather data
      final weatherInfo = await _apiClient.fetchWeather(inverterId);

      if (mounted) {
        setState(() {
          lastInverterData = data;
          weatherData = weatherInfo;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading inverter data: $e')),
        );
      }
    }
  }

  // Callback for when a new inverter is selected
  void onInverterSelected(dynamic inverter) {
    setState(() {
      selectedInverter = inverter;
      _isSwitchingInverter = true;
    });

    final inverterId = int.parse(inverter['id'].toString());

    // Load inverter data for the selected inverter
    _loadInverterData(inverterId).then((_) {
      if (mounted) {
        setState(() {
          _isSwitchingInverter = false;
        });
      }
    });

    // Restart polling with the new inverter ID
    _startPolling(inverterId);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                // Inverter selector dropdown
                if (isLoading) ...[
                  const Center(child: CircularProgressIndicator()),
                  const SizedBox(height: 24),
                ] else if (inverters != null && inverters!.isNotEmpty) ...[
                  const Text(
                    'Select Inverter:',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    width: double.infinity,
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    padding: const EdgeInsets.symmetric(horizontal: 12),
                    child: DropdownButton<dynamic>(
                      value: selectedInverter,
                      isExpanded: true,
                      underline: Container(), // Remove the default underline
                      onChanged: (newValue) {
                        if (newValue != null) {
                          onInverterSelected(newValue);
                        }
                      },
                      items: inverters!.map<DropdownMenuItem<dynamic>>((
                        inverter,
                      ) {
                        return DropdownMenuItem<dynamic>(
                          value: inverter,
                          child: Text(
                            inverter['nickname'] ??
                                inverter['serial_number'] ??
                                'Unknown',
                          ),
                        );
                      }).toList(),
                    ),
                  ),
                  const SizedBox(height: 24),
                ] else ...[
                  const Center(child: Text('No inverters available')),
                  const SizedBox(height: 24),
                ],

                // Display selected inverter details if available
                if (selectedInverter != null) ...[
                  const Text(
                    'Selected Inverter Details:',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Nickname: ${selectedInverter['nickname'] ?? 'N/A'}',
                          ),
                          Text(
                            'Serial Number: ${selectedInverter['serial_number'] ?? 'N/A'}',
                          ),
                          // Add more details as needed
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),
                ],

                // Display last inverter data if available
                if (lastInverterData != null) ...[
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Current Inverter Data:',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      // Add a refresh button
                      IconButton(
                        icon: const Icon(Icons.refresh),
                        onPressed: () {
                          if (selectedInverter != null) {
                            final inverterId = int.parse(
                              selectedInverter['id'].toString(),
                            );
                            _refreshInverterData(inverterId);
                          }
                        },
                        tooltip: 'Refresh data',
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text('SOC: ${lastInverterData!['SOC'] ?? 'N/A'}%'),
                          Text(
                            'Load: ${lastInverterData!['Load_W'] ?? 'N/A'} W',
                          ),
                          Text('PV: ${lastInverterData!['PV_W'] ?? 'N/A'} W'),
                          Text(
                            'Grid: ${lastInverterData!['Grid_W'] ?? 'N/A'} W',
                          ),
                          Text(
                            'Battery: ${lastInverterData!['Battery_W'] ?? 'N/A'} W',
                          ),
                          Text(
                            'Timestamp: ${lastInverterData!['timestamp_utc'] != null ? _formatTimestamp(lastInverterData!['timestamp_utc']) : 'N/A'}',
                          ),
                          // Add a small indicator showing when data was last refreshed
                          Padding(
                            padding: const EdgeInsets.only(top: 8.0),
                            child: Text(
                              'Last updated: ${DateTime.now().toLocal().toString().substring(0, 19)}',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey[600],
                                fontStyle: FontStyle.italic,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),
                ],

                // Display weather data if available
                if (weatherData != null) ...[
                  const Text(
                    'Weather Data:',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (weatherData!['forecast'] != null &&
                              weatherData!['forecast'].isNotEmpty) ...[
                            Text(
                              'Temperature: ${weatherData!['forecast'][0]['temperature'] ?? 'N/A'}°C',
                            ),
                            Text(
                              'Condition: ${weatherData!['forecast'][0]['condition'] ?? 'N/A'}',
                            ),
                            Text(
                              'Cloud Cover: ${weatherData!['forecast'][0]['cloud_cover'] ?? 'N/A'}%',
                            ),
                          ] else ...[
                            const Text('No weather data available'),
                          ],
                        ],
                      ),
                    ),
                  ),
                  ElevatedButton(
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => SocSettingsScreen(
                            inverterId: int.parse(
                              selectedInverter!['id'].toString(),
                            ),
                          ),
                        ),
                      );
                    },
                    child: const Text('SOC Settings'),
                  ),
                ],

                const Spacer(),
                Center(
                  child: ElevatedButton(
                    onPressed: () async {
                      await _apiClient.logout();
                      if (!mounted) return;
                      Navigator.pushReplacementNamed(context, '/login');
                    },
                    child: const Text('Logout'),
                  ),
                ),
              ],
            ),
          ),
          // Loading overlay when switching inverters
          if (_isSwitchingInverter)
            Container(
              color: Colors.black54,
              child: const Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text(
                      'Loading inverter data...',
                      style: TextStyle(color: Colors.white),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  // Helper method to format timestamp
  String _formatTimestamp(String timestamp) {
    final dateTime = DateTime.parse(timestamp).toLocal();
    return '${dateTime.toString().substring(0, 19)}';
  }
}
