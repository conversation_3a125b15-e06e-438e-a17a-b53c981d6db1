import 'package:flutter/material.dart';
import 'package:sunsynk_flutter/services/api_client.dart';
import 'dart:convert';
import 'package:intl/intl.dart';

class SocSettingsScreen extends StatefulWidget {
  final int inverterId;
  final String? inverterName;

  const SocSettingsScreen({
    Key? key,
    required this.inverterId,
    this.inverterName,
  }) : super(key: key);

  @override
  State<SocSettingsScreen> createState() => _SocSettingsScreenState();
}

class _SocSettingsScreenState extends State<SocSettingsScreen> {
  final ApiClient _apiClient = ApiClient();
  bool _isLoading = true;
  bool _isSaving = false;
  String? _errorMessage;

  // Settings data
  Map<String, dynamic> _currentApiSettings = {};
  Map<String, dynamic> _lastHistoricSettings = {};
  Map<String, dynamic> _lastUserDefinedSettings = {};
  String? _lastHistoricTimestamp;
  String? _lastUserDefinedTimestamp;
  String? _lastHistoricType;
  bool _showAdvancedSettings = false;
  final TextEditingController _globalSocController = TextEditingController(
    text: '100',
  );

  // Current source tracking
  String _currentSource = 'Current API';

  // Inverter details
  String? _inverterSerial;

  // History data
  List<dynamic> _historyData = [];

  // Form controllers
  final List<TimeOfDay?> _timeControllers = List.generate(6, (_) => null);
  final List<TextEditingController> _socControllers = List.generate(
    6,
    (_) => TextEditingController(),
  );

  @override
  void initState() {
    super.initState();
    _fetchCurrentSettings();
  }

  @override
  void dispose() {
    for (var controller in _socControllers) {
      controller.dispose();
    }
    _globalSocController.dispose();
    super.dispose();
  }

  Future<void> _fetchCurrentSettings() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final data = await _apiClient.fetchSocSettings(widget.inverterId);

      setState(() {
        // Extract the different settings types
        _currentApiSettings = data['current_api_settings'] ?? {};
        _lastHistoricSettings = data['last_historic_settings'] ?? {};
        _lastUserDefinedSettings = data['last_user_defined_settings'] ?? {};
        _lastHistoricTimestamp = data['last_historic_timestamp'];
        _lastUserDefinedTimestamp = data['last_user_defined_timestamp'];
        _lastHistoricType = data['last_historic_type'];

        // Add these settings to the history data if they're not empty
        _historyData = [];

        if (_currentApiSettings.isNotEmpty) {
          _historyData.add({
            'timestamp': DateTime.now().toIso8601String(),
            'settings_type': 'api',
            'applied_by': 'System',
            'notes': 'Current API settings',
            'soc_settings': _currentApiSettings,
          });
        }

        if (_lastHistoricSettings.isNotEmpty &&
            _lastHistoricTimestamp != null) {
          _historyData.add({
            'timestamp': _lastHistoricTimestamp,
            'settings_type': _lastHistoricType ?? 'historic',
            'applied_by': 'System',
            'notes': 'Last historic settings',
            'soc_settings': _lastHistoricSettings,
          });
        }

        if (_lastUserDefinedSettings.isNotEmpty &&
            _lastUserDefinedTimestamp != null) {
          _historyData.add({
            'timestamp': _lastUserDefinedTimestamp,
            'settings_type': 'user_defined',
            'applied_by': 'User',
            'notes': 'Last user-defined settings',
            'soc_settings': _lastUserDefinedSettings,
          });
        }

        // Use current API settings to initialize controllers
        Map<String, dynamic> settingsToUse = _currentApiSettings;

        // Initialize controllers with current values
        for (int i = 1; i <= 6; i++) {
          final timeKey = 'sellTime$i';
          final socKey = 'cap$i';

          if (settingsToUse.containsKey(timeKey) &&
              settingsToUse[timeKey] != null) {
            final timeParts = settingsToUse[timeKey].split(':');
            if (timeParts.length == 2) {
              _timeControllers[i - 1] = TimeOfDay(
                hour: int.parse(timeParts[0]),
                minute: int.parse(timeParts[1]),
              );
            }
          }

          if (settingsToUse.containsKey(socKey) &&
              settingsToUse[socKey] != null) {
            _socControllers[i - 1].text = settingsToUse[socKey].toString();
          }
        }

        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Error: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  Future<void> _saveSettings() async {
    // Validate inputs
    for (int i = 0; i < 6; i++) {
      if (_timeControllers[i] == null || _socControllers[i].text.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('All slots must have time and SOC values'),
          ),
        );
        return;
      }

      final socValue = int.tryParse(_socControllers[i].text);
      if (socValue == null || socValue < 10 || socValue > 100) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('SOC values must be between 10 and 100'),
          ),
        );
        return;
      }
    }

    setState(() {
      _isSaving = true;
      _errorMessage = null;
    });

    try {
      // Prepare settings data
      final Map<String, dynamic> socSettings = {};

      for (int i = 0; i < 6; i++) {
        final time = _timeControllers[i]!;
        final timeString =
            '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
        final socValue = int.parse(_socControllers[i].text);

        socSettings['sellTime${i + 1}'] = timeString;
        socSettings['cap${i + 1}'] = socValue;
      }

      final response = await _apiClient.updateSocSettings(
        widget.inverterId,
        socSettings,
      );

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Settings saved successfully')),
      );
      // Refresh data
      _fetchCurrentSettings();
      setState(() {
        _currentSource = 'Current API';
        _isSaving = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Error: ${e.toString()}';
        _isSaving = false;
      });
    }
  }

  Future<void> _selectTime(BuildContext context, int index) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: _timeControllers[index] ?? TimeOfDay.now(),
    );

    if (picked != null) {
      setState(() {
        _timeControllers[index] = picked;
      });
    }
  }

  Future<void> _loadSettingsFromSource(
    Map<String, dynamic> settings,
    String sourceName,
  ) {
    if (settings.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('No settings available from $sourceName')),
      );
      return Future.value();
    }

    setState(() {
      _currentSource = sourceName;

      for (int i = 1; i <= 6; i++) {
        final timeKey = 'sellTime$i';
        final socKey = 'cap$i';

        if (settings.containsKey(timeKey) && settings[timeKey] != null) {
          final timeParts = settings[timeKey].split(':');
          if (timeParts.length == 2) {
            _timeControllers[i - 1] = TimeOfDay(
              hour: int.parse(timeParts[0]),
              minute: int.parse(timeParts[1]),
            );
          }
        }

        if (settings.containsKey(socKey) && settings[socKey] != null) {
          _socControllers[i - 1].text = settings[socKey].toString();
        }
      }
    });

    return Future.value();
  }

  String _getSettingsTypeLabel(String? type) {
    switch (type) {
      case 'user_defined':
        return 'User Settings';
      case 'boost':
        return 'Boost';
      case 'auto':
        return 'Auto';
      case 'restored_from_user':
        return 'Restored User';
      default:
        return 'Unknown';
    }
  }

  Color _getSettingsTypeColor(String? type) {
    switch (type) {
      case 'user_defined':
        return Colors.green;
      case 'boost':
        return Colors.orange;
      case 'auto':
        return Colors.blue;
      case 'restored_from_user':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  String _formatDateTime(String? dateTimeStr) {
    if (dateTimeStr == null) return 'N/A';
    try {
      final dateTime = DateTime.parse(dateTimeStr).toLocal();
      return DateFormat('yyyy-MM-dd HH:mm').format(dateTime);
    } catch (e) {
      return dateTimeStr;
    }
  }

  void _applyGlobalSocValue() async {
    final socValue = int.tryParse(_globalSocController.text);
    if (socValue == null || socValue < 10 || socValue > 100) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('SOC value must be between 10 and 100')),
      );
      return;
    }

    // Set all SOC controllers to the same value
    for (var controller in _socControllers) {
      controller.text = socValue.toString();
    }

    // Save the settings immediately
    await _saveSettings();

    // Show success message
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('SOC values updated and saved successfully'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  bool _areAllSocValuesSame() {
    if (_socControllers.isEmpty) return true;

    final firstValue = _socControllers[0].text;
    return _socControllers.every((controller) => controller.text == firstValue);
  }

  void _onSocControllerChanged() {
    setState(() {
      // This will trigger a rebuild to update the button/warning visibility
    });
  }

  @override
  Widget build(BuildContext context) {
    final inverterDisplayName =
        widget.inverterName ??
        _inverterSerial ??
        'Inverter #${widget.inverterId}';

    return Scaffold(
      appBar: AppBar(title: Text('SOC Settings - $inverterDisplayName')),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _buildContent(),
    );
  }

  Widget _buildContent() {
    return DefaultTabController(
      length: 3,
      child: Column(
        children: [
          const TabBar(
            tabs: [
              Tab(text: 'Edit Settings'),
              Tab(text: 'Boost'),
              Tab(text: 'History'),
            ],
            labelColor: Colors.blue,
          ),
          Expanded(
            child: TabBarView(
              children: [
                _buildSettingsForm(),
                _buildBoostView(),
                _buildHistoryView(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _startBoost() async {
    // Show dialog to get target SOC
    final targetSoc = await showDialog<int>(
      context: context,
      builder: (context) => _buildBoostDialog(),
    );

    if (targetSoc == null) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final response = await _apiClient.boost({
        'inverterId': widget.inverterId,
        'soc': targetSoc,
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Boost to ${targetSoc}% started successfully'),
          backgroundColor: Colors.green,
        ),
      );

      // Refresh data to show new state
      _fetchCurrentSettings();
    } catch (e) {
      setState(() {
        _errorMessage = 'Error: ${e.toString()}';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  // Dialog to get boost parameters
  Widget _buildBoostDialog() {
    final controller = TextEditingController(text: '100');

    return AlertDialog(
      title: const Text('Boost Battery'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Text('Set target SOC percentage:'),
          TextField(
            controller: controller,
            keyboardType: TextInputType.number,
            decoration: const InputDecoration(
              suffix: Text('%'),
              hintText: 'Enter target SOC (10-100)',
            ),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            final value = int.tryParse(controller.text);
            if (value != null && value >= 10 && value <= 100) {
              Navigator.of(context).pop(value);
            } else {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Please enter a valid SOC between 10 and 100'),
                ),
              );
            }
          },
          child: const Text('Start Boost'),
        ),
      ],
    );
  }

  Widget _buildBoostView() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Battery Boost',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          const Text(
            'Quickly charge your battery to a target SOC level.',
            style: TextStyle(fontSize: 16, color: Colors.grey),
          ),
          const SizedBox(height: 24),

          ElevatedButton.icon(
            icon: const Icon(Icons.battery_charging_full),
            label: const Text('Start Boost'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
              minimumSize: const Size(double.infinity, 48),
            ),
            onPressed: _isLoading ? null : _startBoost,
          ),

          if (_errorMessage != null) ...[
            const SizedBox(height: 16),
            Text(_errorMessage!, style: const TextStyle(color: Colors.red)),
          ],
        ],
      ),
    );
  }

  Widget _buildSettingsForm() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Source info
          if (_lastHistoricTimestamp != null ||
              _lastUserDefinedTimestamp != null)
            Card(
              margin: const EdgeInsets.symmetric(vertical: 16),
              child: Padding(
                padding: const EdgeInsets.all(12.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (_lastHistoricTimestamp != null) ...[
                      Row(
                        children: [
                          const Text(
                            'Last Historic: ',
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                          Text(_formatDateTime(_lastHistoricTimestamp)),
                          const SizedBox(width: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 6,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: _getSettingsTypeColor(_lastHistoricType),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              _getSettingsTypeLabel(_lastHistoricType ?? ''),
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                    ],
                    if (_lastUserDefinedTimestamp != null) ...[
                      Row(
                        children: [
                          const Text(
                            'Last User Defined: ',
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                          Text(_formatDateTime(_lastUserDefinedTimestamp)),
                        ],
                      ),
                    ],
                  ],
                ),
              ),
            ),

          // Quick SOC setter
          Card(
            margin: const EdgeInsets.symmetric(vertical: 16),
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Quick SOC Setting',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  if (!_areAllSocValuesSame() && !_isSaving) ...[
                    const Text(
                      'Warning: SOC values are not all the same. Set individual values below or make them all the same to use bulk setting.',
                      style: TextStyle(color: Colors.orange),
                    ),
                    const SizedBox(height: 16),
                  ],
                  if (_areAllSocValuesSame() && !_isSaving) ...[
                    Wrap(
                      spacing: 12,
                      runSpacing: 12,
                      alignment: WrapAlignment.start,
                      crossAxisAlignment: WrapCrossAlignment.center,
                      children: [
                        const Text('New SOC:'),
                        SizedBox(
                          width: 80,
                          child: TextField(
                            controller: _globalSocController,
                            keyboardType: TextInputType.number,
                            decoration: const InputDecoration(
                              suffix: Text('%'),
                              border: OutlineInputBorder(),
                              contentPadding: EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 8,
                              ),
                            ),
                          ),
                        ),
                        ElevatedButton(
                          onPressed: _isSaving ? null : _applyGlobalSocValue,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green,
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 8,
                            ),
                          ),
                          child: _isSaving
                              ? const SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    color: Colors.white,
                                  ),
                                )
                              : const Text('Apply & Save'),
                        ),
                      ],
                    ),
                  ],
                  const SizedBox(height: 16),
                  TextButton.icon(
                    onPressed: () {
                      setState(() {
                        _showAdvancedSettings = !_showAdvancedSettings;
                      });
                    },
                    icon: Icon(
                      _showAdvancedSettings
                          ? Icons.keyboard_arrow_up
                          : Icons.keyboard_arrow_down,
                    ),
                    label: Text(
                      _showAdvancedSettings
                          ? 'Hide Advanced Settings'
                          : 'Show Advanced Settings',
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          if (_errorMessage != null)
            Padding(
              padding: const EdgeInsets.only(bottom: 16),
              child: Text(
                _errorMessage!,
                style: const TextStyle(color: Colors.red),
              ),
            ),

          // Advanced settings (individual time slots)
          if (_showAdvancedSettings) ...[
            const Text(
              'Configure SOC Time Slots',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    for (int i = 0; i < 6; i++)
                      Padding(
                        padding: const EdgeInsets.only(bottom: 16.0),
                        child: Row(
                          children: [
                            Expanded(flex: 2, child: Text('Slot ${i + 1}')),
                            Expanded(
                              flex: 4,
                              child: InkWell(
                                onTap: () => _selectTime(context, i),
                                child: InputDecorator(
                                  decoration: const InputDecoration(
                                    labelText: 'Time',
                                    border: OutlineInputBorder(),
                                  ),
                                  child: Text(
                                    _timeControllers[i] != null
                                        ? '${_timeControllers[i]!.hour.toString().padLeft(2, '0')}:${_timeControllers[i]!.minute.toString().padLeft(2, '0')}'
                                        : 'Select Time',
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              flex: 3,
                              child: TextField(
                                controller: _socControllers[i],
                                decoration: const InputDecoration(
                                  labelText: 'SOC %',
                                  border: OutlineInputBorder(),
                                  suffixText: '%',
                                ),
                                keyboardType: TextInputType.number,
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ],

          const SizedBox(height: 24),

          if (_showAdvancedSettings)
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isSaving ? null : _saveSettings,
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: _isSaving
                    ? const CircularProgressIndicator()
                    : const Text('Save Settings'),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildHistoryView() {
    if (_historyData.isEmpty) {
      return const Center(child: Text('No history available'));
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16.0),
      itemCount: _historyData.length,
      itemBuilder: (context, index) {
        final item = _historyData[index];
        final timestamp = DateTime.parse(item['timestamp']).toLocal();
        final settingsType = item['settings_type'];
        final appliedBy = item['applied_by'] ?? 'System';
        final notes = item['notes'] ?? '';
        final socSettings = item['soc_settings'] ?? {};

        return Card(
          margin: const EdgeInsets.only(bottom: 16.0),
          child: ExpansionTile(
            title: Text(
              DateFormat('yyyy-MM-dd HH:mm:ss').format(timestamp),
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            subtitle: Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: _getSettingsTypeColor(settingsType),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    _getSettingsTypeLabel(settingsType),
                    style: const TextStyle(color: Colors.white, fontSize: 12),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'By: $appliedBy',
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            children: [
              if (notes.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: Text('Notes: $notes'),
                  ),
                ),
              const Divider(),
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'SOC Settings:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    _buildSocSettingsTable(socSettings),
                    const SizedBox(height: 16),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        icon: const Icon(Icons.restore),
                        label: const Text('Load These Settings'),
                        onPressed: () {
                          _loadSettingsFromSource(
                            socSettings,
                            'History (${DateFormat('yyyy-MM-dd').format(timestamp)})',
                          );
                          DefaultTabController.of(
                            context,
                          ).animateTo(0); // Switch to edit tab
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSocSettingsTable(Map<String, dynamic> socSettings) {
    if (socSettings.isEmpty) {
      return const Text('No SOC settings available');
    }

    return Table(
      border: TableBorder.all(color: Colors.grey.shade300),
      columnWidths: const {
        0: FlexColumnWidth(1),
        1: FlexColumnWidth(2),
        2: FlexColumnWidth(2),
      },
      children: [
        const TableRow(
          decoration: BoxDecoration(color: Colors.grey),
          children: [
            Padding(
              padding: EdgeInsets.all(8.0),
              child: Text(
                'Slot',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
            ),
            Padding(
              padding: EdgeInsets.all(8.0),
              child: Text(
                'Time',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
            ),
            Padding(
              padding: EdgeInsets.all(8.0),
              child: Text(
                'SOC %',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
            ),
          ],
        ),
        for (int i = 1; i <= 6; i++)
          if (socSettings.containsKey('sellTime$i') ||
              socSettings.containsKey('cap$i'))
            TableRow(
              children: [
                Padding(padding: const EdgeInsets.all(8.0), child: Text('$i')),
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text(socSettings['sellTime$i'] ?? 'N/A'),
                ),
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text(
                    socSettings['cap$i'] != null
                        ? '${socSettings['cap$i']}%'
                        : 'N/A',
                  ),
                ),
              ],
            ),
      ],
    );
  }
}
