import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

class ApiClient {
  final String baseUrl;

  ApiClient({this.baseUrl = 'https://killwatt.co.za'});

  // Get the auth token from SharedPreferences
  Future<String?> _getToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('token');
  }

  // Generic GET request with authentication
  Future<http.Response> get(
    String endpoint, {
    Map<String, String>? queryParams,
  }) async {
    final token = await _getToken();

    if (token == null) {
      return http.Response('Not authenticated', 401);
    }

    final uri = Uri.parse(
      '$baseUrl$endpoint',
    ).replace(queryParameters: queryParams);

    return http.get(uri, headers: {'X-Session-Token': token});
  }

  // Generic POST request with authentication
  Future<http.Response> post(
    String endpoint, {
    Map<String, dynamic>? body,
  }) async {
    final token = await _getToken();

    if (token == null) {
      return http.Response('Not authenticated', 401);
    }

    return http.post(
      Uri.parse('$baseUrl$endpoint'),
      headers: {'X-Session-Token': token, 'Content-Type': 'application/json'},
      body: body != null ? jsonEncode(body) : null,
    );
  }

  // Fetch inverters list
  Future<List<dynamic>> fetchInverters() async {
    final response = await get('/core/api/inverters/');

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Failed to load inverters: ${response.statusCode}');
    }
  }

  // Fetch weather data for a specific inverter
  Future<Map<String, dynamic>> fetchWeather(
    int inverterId, {
    DateTime? from,
    DateTime? to,
  }) async {
    from ??= DateTime.now().subtract(const Duration(days: 1));
    to ??= DateTime.now();

    final queryParams = {
      'from': from.toIso8601String(),
      'to': to.toIso8601String(),
    };

    final response = await get(
      '/core/inverter/$inverterId/weather/',
      queryParams: queryParams,
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Failed to load weather data: ${response.statusCode}');
    }
  }

  // Login method
  Future<String?> login(String username, String password) async {
    final response = await http.post(
      Uri.parse('$baseUrl/auth/login/'),
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({'username': username, 'password': password}),
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      final token = data['token'];

      // Save token to SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('token', token);
      await prefs.setString('token_date', DateTime.now().toString());

      return token;
    } else {
      return null;
    }
  }

  // Email/password login
  Future<Map<String, dynamic>> loginWithEmail(
    String email,
    String password,
  ) async {
    final response = await http.post(
      Uri.parse('$baseUrl/_allauth/app/v1/auth/login'),
      body: jsonEncode({'email': email, 'password': password}),
      headers: {
        'Content-Type': 'application/json',
        'Referer': 'https://killwatt.co.za/',
      },
    );

    if (response.statusCode == 200) {
      final responseData = jsonDecode(response.body);
      final meta = responseData['meta'];
      final token = meta['session_token'];
      final isAuthenticated = meta['is_authenticated'];

      if (!isAuthenticated) {
        throw Exception('Authentication failed');
      }

      // Save token to SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('token', token);
      await prefs.setString('token_date', DateTime.now().toString());

      return responseData;
    } else {
      if (response.statusCode == 400) {
        throw Exception('Login failed: Invalid email or password');
      }

      throw Exception('Login failed: Unknown error');
    }
  }

  // Google sign-in
  Future<Map<String, dynamic>> loginWithGoogle(
    String accessToken,
    String idToken,
    String clientId,
  ) async {
    final response = await http.post(
      Uri.parse('$baseUrl/_allauth/app/v1/auth/provider/token'),
      body: jsonEncode({
        'provider': 'google',
        'process': 'login',
        'token': {
          'access_token': accessToken,
          'id_token': idToken,
          'client_id': clientId,
        },
      }),
      headers: {
        'Content-Type': 'application/json',
        'Referer': 'https://killwatt.co.za/',
      },
    );

    if (response.statusCode == 200) {
      final responseData = jsonDecode(response.body);
      final meta = responseData['meta'];
      final token = meta['session_token'];
      final isAuthenticated = meta['is_authenticated'];

      if (!isAuthenticated) {
        throw Exception('Authentication failed');
      }

      // Save token to SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('token', token);
      await prefs.setString('token_date', DateTime.now().toString());

      return responseData;
    } else {
      throw Exception('Google login failed: ${response.body}');
    }
  }

  // Email/password signup
  Future<Map<String, dynamic>> signupWithEmail(
    String email,
    String password,
  ) async {
    final response = await http.post(
      Uri.parse('$baseUrl/_allauth/app/v1/auth/signup'),
      body: jsonEncode({'email': email, 'password': password}),
      headers: {
        'Content-Type': 'application/json',
        'Referer': 'https://killwatt.co.za/',
      },
    );

    if (response.statusCode == 200) {
      final responseData = jsonDecode(response.body);
      final meta = responseData['meta'];
      final token = meta['session_token'];
      final isAuthenticated = meta['is_authenticated'];

      if (!isAuthenticated) {
        throw Exception('Signup failed');
      }

      // Save token to SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('token', token);
      await prefs.setString('token_date', DateTime.now().toString());

      return responseData;
    } else {
      final responseData = jsonDecode(response.body);
      final errorMessage =
          responseData['errors'][0]['message'] ?? 'Signup failed';
      throw Exception(errorMessage);
    }
  }
  
  // Request password reset
  Future<Map<String, dynamic>> requestPasswordReset(String email) async {
    final response = await http.post(
      Uri.parse('$baseUrl/_allauth/app/v1/auth/password/request'),
      body: jsonEncode({'email': email}),
      headers: {
        'Content-Type': 'application/json',
        'Referer': 'https://killwatt.co.za/',
      },
    );

    if (response.statusCode == 200) {
        return jsonDecode(response.body);
    } else {
      final responseData = jsonDecode(response.body);
      print(responseData);
      final errorMessage =
          responseData['errors'][0]['message'] ?? 'Password reset failed';
      throw Exception(errorMessage);
    }
  }

  // Logout method
  Future<void> logout() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('token');
    await prefs.remove('token_date');
  }

  // Fetch last inverter data for a specific inverter
  Future<Map<String, dynamic>> fetchLastInverterData(int inverterId) async {
    final response = await get('/core/api/inverter/$inverterId/last-data/');

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Failed to load inverter data: ${response.statusCode}');
    }
  }

  // Fetch SOC settings for a specific inverter
  Future<Map<String, dynamic>> fetchSocSettings(int inverterId) async {
    final response = await get('/core/api/soc/$inverterId/');

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Failed to load SOC settings: ${response.statusCode}');
    }
  }

  // Update SOC settings for a specific inverter
  Future<Map<String, dynamic>> updateSocSettings(
    int inverterId,
    Map<String, dynamic> socSettings,
  ) async {
    final response = await post(
      '/core/api/soc/$inverterId/set/',
      body: socSettings,
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Failed to update SOC settings: ${response.statusCode}');
    }
  }

  Future<Map<String, dynamic>> boost(Map<String, dynamic> params) async {
    final response = await post(
      '/core/api/soc/${params['inverterId']}/boost/',
      body: params,
    );
    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception(
        'Failed to start boost: ${response.statusCode}: ${response.body}',
      );
    }
  }
}
